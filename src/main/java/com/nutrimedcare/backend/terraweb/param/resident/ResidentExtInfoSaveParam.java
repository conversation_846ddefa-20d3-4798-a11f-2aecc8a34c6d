package com.nutrimedcare.backend.terraweb.param.resident;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ResidentExtInfoSaveParam {

    private Long id;

    @NotNull(message = "长者ID不能为空")
    private Long residentId;

    /**
     * @see com.nutrimedcare.backend.terraweb.enums.resident.ResidentExtInfoTypeEnum
     */
    @NotNull(message = "类型不能为空")
    private Integer extType;

    @NotBlank(message = "信息不能为空")
    private String extValue;

}